<!--
  机构放款金额Top5组件
  功能：
  1. 展示机构放款金额的前5名数据
  2. 提供3D柱状图可视化展示
  3. 显示累计放款金额和笔数
  4. 支持图表交互和提示
-->
<template>
  <div class="AmountTop5-dataAcquisition">
    <!-- 头部标题区域 -->
    <div class="AmountTop5-headerImg">
      <div>机构放款动态</div>
      <div class="ChainDistribution-rightClass">
        <div class="ChainDistribution-djgdClass" @click="handleClick">
          点击查看更多
        </div>
        <div>
          <img
            src="@/assets/dp/rightT.png"
            class="ChainDistribution-imgRight"
          />
        </div>
      </div>
    </div>
    <div class="AmountTop5-bottomClass">
      <!-- 顶部统计信息 -->
      <div class="AmountTop5-top-summary">
        <span>累计放款金额：</span>
        <span class="AmountTop5-highlight">{{ amount }}</span
        >亿元 <span class="AmountTop5-highlight2">{{ parseInt(count) }}</span
        >笔
      </div>
      <!-- 图表容器 -->
      <div ref="barChart" class="AmountTop5-echart-bar"></div>
    </div>
    <dialog-bar-park2
      :visible="dialogVisible"
      :data="parkList"
      title="机构放款动态"
      :display-count="12"
      @close="handleDialogClose"
    />
  </div>
</template>

<script>
import * as echarts from "echarts";
import { productUpdateList } from "@/api/article.js";
import DialogBarPark2 from "@/views/components/dialogBar-park2.vue";

export default {
  name: "AmountTop5",
  components: { DialogBarPark2 },
  data() {
    return {
      dialogVisible: false,
      // 银行名称列表
      banks: [
        "中国\n建设银行",
        "中国\n农业银行",
        "成都\n银行",
        "中国\n工商银行",
        "中国\n银行",
      ],
      values: [], // 放款金额数据（单位：亿）
      amount: 0, // 累计放款金额
      count: 0, // 累计放款笔数
      counts: [3222, 2888, 2540, 2100, 1800], // 放款笔数数据
      dialogAutoPlayTimer: null, // 自动播放定时器
      timerIndex: 0, // 当前播放的索引
      addResizeListener: false, // 是否添加了resize监听器
      parkList: [],
      top5List: [], // Top5数据列表
      currentStartIndex: 0, // 轮播的当前起始索引
    };
  },
  mounted() {
    // 获取数据并初始化图表
    productUpdateList([
      {
        bizDate: localStorage.getItem("maxDt"),
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimNum: 0,
        dimIndustryChain: "ALL",
        indexName: "累计放款金额",
      },
      {
        bizDate: localStorage.getItem("maxDt"),
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimNum: 1,
        dimIndustryChain: "ALL",
        indexName: "累计放款笔数",
      },
      {
        bizDate: localStorage.getItem("maxDt"),
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimNum: 1,
        dimIndustryChain: "ALL",
        indexName: "金融机构放款金额",
      },
    ]).then((res) => {
      this.amount = res.data.data[0].indexValue;
      this.count = res.data.data[1].indexValue;
      this.top5List = JSON.parse(res.data.data[2].bizContent);
      this.parkList = this.top5List
        .map((item) => {
          return { ...item, value: item.loanAmt.replace("亿元", "") };
        })
        .sort((a, b) => b.value - a.value);
      // 处理数据：去除单位并转换为数字
      this.values = this.top5List.map((item) =>
        item.loanAmt.replace("亿元", "")
      );
      this.values = this.values.map((item) => Number(item));

      this.counts = this.top5List.map((item) => item.loanSum);
      this.banks = this.top5List.map((item) => item.shortName);
      this.initBarChart();
    });
  },
  methods: {
    // 初始化3D柱状图
    initBarChart() {
      const chart = echarts.init(this.$refs.barChart);
      const offsetX = 10; // X轴偏移量
      const offsetY = 5; // Y轴偏移量

      // 绘制左侧面
      const CubeLeft = echarts.graphic.extendShape({
        shape: {
          x: 0,
          y: 0,
        },
        buildPath: function (ctx, shape) {
          const xAxisPoint = shape.xAxisPoint;
          const c0 = [shape.x, shape.y];
          const c1 = [shape.x - offsetX, shape.y - offsetY];
          const c2 = [xAxisPoint[0] - offsetX, xAxisPoint[1] - offsetY];
          const c3 = [xAxisPoint[0], xAxisPoint[1]];
          ctx
            .moveTo(c0[0], c0[1])
            .lineTo(c1[0], c1[1])
            .lineTo(c2[0], c2[1])
            .lineTo(c3[0], c3[1])
            .closePath();
        },
      });

      // 绘制右侧面
      const CubeRight = echarts.graphic.extendShape({
        shape: {
          x: 0,
          y: 0,
        },
        buildPath: function (ctx, shape) {
          const xAxisPoint = shape.xAxisPoint;
          const c1 = [shape.x, shape.y];
          const c2 = [xAxisPoint[0], xAxisPoint[1]];
          const c3 = [xAxisPoint[0] + offsetX, xAxisPoint[1] - offsetY];
          const c4 = [shape.x + offsetX, shape.y - offsetY];
          ctx
            .moveTo(c1[0], c1[1])
            .lineTo(c2[0], c2[1])
            .lineTo(c3[0], c3[1])
            .lineTo(c4[0], c4[1])
            .closePath();
        },
      });

      // 绘制顶面
      const CubeTop = echarts.graphic.extendShape({
        shape: {
          x: 0,
          y: 0,
        },
        buildPath: function (ctx, shape) {
          const c1 = [shape.x, shape.y];
          const c2 = [shape.x + offsetX, shape.y - offsetY]; //右点
          const c3 = [shape.x, shape.y - offsetX];
          const c4 = [shape.x - offsetX, shape.y - offsetY];
          ctx
            .moveTo(c1[0], c1[1])
            .lineTo(c2[0], c2[1])
            .lineTo(c3[0], c3[1])
            .lineTo(c4[0], c4[1])
            .closePath();
        },
      });

      // 注册三个面图形
      echarts.graphic.registerShape("CubeLeft", CubeLeft);
      echarts.graphic.registerShape("CubeRight", CubeRight);
      echarts.graphic.registerShape("CubeTop", CubeTop);

      this.updateEcharts(chart, 0, 5, this.values, this.counts, this.banks);
      if (this.addResizeListener) return;
      // 监听窗口大小变化，调整图表大小
      window.addEventListener("resize", () => chart.resize());
    },
    // 初始化图表并启动轮播动画
    updateEcharts(chart, startIndex, length, values, counts, banks) {
      const primaryColor = "42,205,253"; // 主色调
      const isMaxShow = true; // 是否显示最大值
      const displayCount = 5; // 默认显示5个银行

      // 如果数据超过5个，为了轮播效果，我们需要准备更多数据
      // 但初始只显示前5个
      const displayValues = values.slice(0, displayCount);
      const displayCounts = counts.slice(0, displayCount);
      const displayBanks = banks.slice(0, displayCount);
      // 图表配置项
      const option = {
        // 动画配置
        animation: true,
        animationDuration: 1000,
        animationEasing: 'cubicOut',
        animationDelay: 0,
        // 提示框配置
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          formatter: function (params) {
            const item = params[1];
            const value = item.value;
            const count = item.data && item.data.count ? item.data.count : 0;
            return (
              '<div style="text-align:center;">' +
              `<span style="color:#ffce7c;font-size:16px;">${value}亿</span><br/>` +
              `<span style="color:#7cebff;font-size:16px;">${count}笔</span>` +
              "</div>"
            );
          },
          backgroundColor: "rgba(10,29,56,0.95)",
          borderColor: "#0a4d8f",
          borderWidth: 1,
          extraCssText: "box-shadow:0 0 8px #0a4d8f;",
        },
        // 图表网格配置
        grid: {
          left: "0%",
          right: "0%",
          top: "30%",
          bottom: "2%",
          containLabel: true,
        },
        // X轴配置
        xAxis: {
          type: "category",
          data: displayBanks,
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: "rgba(255,255,255,0.2)",
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#fff",
            fontSize: this.$autoFontSize(14),
            interval: 0,
            formatter: function (value) {
              return value.replace(/(.{4})/g, "$1\n");
            },
          },
        },
        // Y轴配置
        yAxis: {
          show: false,
        },
        // 系列配置
        series: [
          // 最大值系列
          isMaxShow
            ? {
                type: "custom",
                renderItem: function (params, api) {
                  const location = api.coord([api.value(0), api.value(1)]);
                  return {
                    type: "group",
                    children: [
                      {
                        type: "CubeLeft",
                        shape: {
                          api,
                          x: location[0],
                          y: location[1],
                          xAxisPoint: api.coord([api.value(0), 0]),
                        },
                        style: {
                          fill: `rgba(${primaryColor}, .1)`,
                        },
                      },
                      {
                        type: "CubeRight",
                        shape: {
                          api,
                          x: location[0],
                          y: location[1],
                          xAxisPoint: api.coord([api.value(0), 0]),
                        },
                        style: {
                          fill: `rgba(${primaryColor}, .3)`,
                        },
                      },
                      {
                        type: "CubeTop",
                        shape: {
                          api,
                          x: location[0],
                          y: location[1],
                          xAxisPoint: api.coord([api.value(0), 0]),
                        },
                        style: {
                          fill: `rgba(${primaryColor}, .4)`,
                        },
                      },
                    ],
                  };
                },
                data: displayValues,
              }
            : null,
          // 实际值系列
          {
            type: "custom",
            renderItem: (params, api) => {
              const location = api.coord([api.value(0), api.value(1)]);
              return {
                type: "group",
                children: [
                  {
                    type: "CubeLeft",
                    shape: {
                      api,
                      xValue: api.value(0),
                      yValue: api.value(1),
                      x: location[0],
                      y: location[1],
                      xAxisPoint: api.coord([api.value(0), 0]),
                    },
                    style: {
                      fill: `rgba(${primaryColor}, .5)`,
                    },
                  },
                  {
                    type: "CubeRight",
                    shape: {
                      api,
                      xValue: api.value(0),
                      yValue: api.value(1),
                      x: location[0],
                      y: location[1],
                      xAxisPoint: api.coord([api.value(0), 0]),
                    },
                    style: {
                      fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                          offset: 0,
                          color: `rgba(${primaryColor},1)`,
                        },
                        {
                          offset: 1,
                          color: `rgba(${primaryColor},.5)`,
                        },
                      ]),
                    },
                  },
                  {
                    type: "CubeTop",
                    shape: {
                      api,
                      xValue: api.value(0),
                      yValue: api.value(1),
                      x: location[0],
                      y: location[1],
                      xAxisPoint: api.coord([api.value(0), 0]),
                    },
                    style: {
                      fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                          offset: 0,
                          color: `rgba(${primaryColor},1)`,
                        },
                        {
                          offset: 1,
                          color: `#419EFF`,
                        },
                      ]),
                    },
                  },
                ],
              };
            },
            data: displayValues.map((value, index) => ({
              value: value,
              count: displayCounts[index]
            })),
          },
          // 标签系列
          {
            type: "bar",
            label: {
              show: true,
              position: "top",
              padding: [0, 0, this.$autoFontSize(5), 0],
              formatter: (params) => {
                const value = displayValues[params.dataIndex];
                const count = displayCounts[params.dataIndex];
                return `{gold|${value}亿元}\n{blue|${count}}`;
              },
              rich: {
                gold: {
                  color: "#ffce7c",
                  fontSize: this.$autoFontSize(14),
                  padding: [0, 0, this.$autoFontSize(5), 0],
                  align: "center",
                },
                blue: {
                  color: "#7cebff",
                  fontSize: this.$autoFontSize(14),
                  align: "center",
                },
              },
            },
            itemStyle: {
              color: "transparent",
            },
            tooltip: {},
            data: displayValues,
          },
        ],
      };
      chart.setOption(option);

      // 启动轮播动画
      this.startAutoScroll(chart);
    },

    // 启动自动轮播动画
    startAutoScroll(chart) {
      if (this.dialogAutoPlayTimer) {
        clearInterval(this.dialogAutoPlayTimer);
      }

      // 确保数据按金额从大到小排序
      const sortedData = [...this.top5List].sort((a, b) => parseFloat(b.loanAmt.replace("亿元", "")) - parseFloat(a.loanAmt.replace("亿元", "")));

      this.dialogAutoPlayTimer = setInterval(() => {
        // 如果数据不足以轮播，直接返回
        if (sortedData.length <= 5) {
          return;
        }

        this.performCarouselAnimation(chart, sortedData);
      }, 3000); // 每3秒轮播一次
    },

    // 执行轮播动画
    performCarouselAnimation(chart, sortedData) {
      // 获取当前图表中显示的数据
      const option = chart.getOption();
      const currentBanks = option.xAxis[0].data;

      // 第一步：让第一个柱子"飞出"（降低高度）
      const flyOutValues = [...currentBanks].map((bankName, index) => {
        const item = sortedData.find((d) => d.shortName === bankName);
        const originalValue = item ? parseFloat(item.loanAmt.replace("亿元", "")) : 0;
        // 第一个柱子高度降为0，其他保持不变
        return index === 0 ? 0 : originalValue;
      });

      const flyOutCounts = [...currentBanks].map((bankName, index) => {
        const item = sortedData.find((d) => d.shortName === bankName);
        const originalCount = item ? item.loanSum : 0;
        return index === 0 ? 0 : originalCount;
      });

      // 执行"飞出"动画
      chart.setOption({
        animation: true,
        animationDuration: 500,
        animationEasing: 'cubicIn',
        series: [
          {
            data: flyOutValues,
          },
          {
            data: flyOutValues.map((value, index) => ({
              value: value,
              count: flyOutCounts[index]
            })),
          },
          {
            data: flyOutValues,
            label: {
              show: true,
              position: "top",
              padding: [0, 0, this.$autoFontSize(5), 0],
              formatter: (params) => {
                const value = flyOutValues[params.dataIndex];
                const count = flyOutCounts[params.dataIndex];
                return value > 0 ? `{gold|${value}亿元}\n{blue|${count}}` : '';
              },
              rich: {
                gold: {
                  color: "#ffce7c",
                  fontSize: this.$autoFontSize(14),
                  padding: [0, 0, this.$autoFontSize(5), 0],
                  align: "center",
                },
                blue: {
                  color: "#7cebff",
                  fontSize: this.$autoFontSize(14),
                  align: "center",
                },
              },
            },
          },
        ],
      });

      // 第二步：600ms后重新排列数据并"飞入"新柱子
      setTimeout(() => {
        // 重新排序数据 - 将第一个移到最后
        const newBanks = [...currentBanks];
        const firstBank = newBanks.shift();

        // 选择下一个要显示的银行
        const displayedBankNames = new Set(currentBanks);
        const unDisplayedBanks = sortedData.filter(item => !displayedBankNames.has(item.shortName));

        if (unDisplayedBanks.length > 0) {
          newBanks.push(unDisplayedBanks[0].shortName);
        } else {
          newBanks.push(firstBank);
        }

        // 根据新的银行顺序重新组织数据
        const newValues = newBanks.map((bankName) => {
          const item = sortedData.find((d) => d.shortName === bankName);
          return item ? parseFloat(item.loanAmt.replace("亿元", "")) : 0;
        });

        const newCounts = newBanks.map((bankName) => {
          const item = sortedData.find((d) => d.shortName === bankName);
          return item ? item.loanSum : 0;
        });

        // 执行"飞入"动画
        chart.setOption({
          animation: true,
          animationDuration: 800,
          animationEasing: 'elasticOut',
          animationDelay: function (idx) {
            // 最后一个柱子（新进入的）有额外延迟
            return idx === newBanks.length - 1 ? 200 : idx * 50;
          },
          xAxis: {
            data: newBanks,
          },
          series: [
            {
              data: newValues,
            },
            {
              data: newValues.map((value, index) => ({
                value: value,
                count: newCounts[index]
              })),
            },
            {
              data: newValues,
              label: {
                show: true,
                position: "top",
                padding: [0, 0, this.$autoFontSize(5), 0],
                formatter: (params) => {
                  const value = newValues[params.dataIndex];
                  const count = newCounts[params.dataIndex];
                  return `{gold|${value}亿元}\n{blue|${count}}`;
                },
                rich: {
                  gold: {
                    color: "#ffce7c",
                    fontSize: this.$autoFontSize(14),
                    padding: [0, 0, this.$autoFontSize(5), 0],
                    align: "center",
                  },
                  blue: {
                    color: "#7cebff",
                    fontSize: this.$autoFontSize(14),
                    align: "center",
                  },
                },
              },
            },
          ],
        });
      }, 600);
    },
    handleClick() {
      this.dialogVisible = true;
    },
    handleDialogClose() {
      this.dialogVisible = false;
    },
  },
  beforeDestroy() {
    if (this.dialogAutoPlayTimer) {
      clearInterval(this.dialogAutoPlayTimer);
    }
  },
};
</script>

<style scoped lang="scss">
/* 主容器样式 */
.AmountTop5-dataAcquisition {
  width: 940px;
  height: 27vh;
  display: flex;
  flex-direction: column;

  /* 头部标题样式 */
  .AmountTop5-headerImg {
    width: 940px;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 90px;
    box-sizing: border-box;
    background-image: url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #ffffff;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;
    .ChainDistribution-rightClass {
      display: flex;
      align-items: center;
      justify-content: center;

      .ChainDistribution-djgdClass {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 26px;
        color: #77c1ff;
        letter-spacing: 1px;
        cursor: pointer;
        margin-right: 12px;
      }

      .ChainDistribution-imgRight {
        width: 12px;
        height: 22px;
        position: relative;
        top: -1px;
      }
    }
  }

  /* 底部内容区域样式 */
  .AmountTop5-bottomClass {
    width: 100%;
    flex: 1;
    background: linear-gradient(
      180deg,
      rgba(3, 29, 58, 0) 0%,
      rgba(0, 50, 107, 0.64) 100%
    );
    border: 1px solid;
    border-top: 0;
    border-image: linear-gradient(
        169deg,
        rgba(44, 110, 162, 0),
        rgba(47, 115, 169, 1)
      )
      2 2;
    backdrop-filter: blur(7px);
    display: flex;
    flex-direction: column;
    align-items: center;

    /* 顶部统计信息样式 */
    .AmountTop5-top-summary {
      width: 940px;
      text-align: left;
      font-size: 26px;
      color: #fff;
      margin-left: 100px;

      /* 金额高亮样式 */
      .AmountTop5-highlight {
        background: linear-gradient(180deg, #fff 0%, #ffce7c 100%);
        font-family: OPPOSans, OPPOSans;
        font-weight: bold;
        font-size: 44px;
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      /* 笔数高亮样式 */
      .AmountTop5-highlight2 {
        margin-left: 20px;
        font-family: OPPOSans, OPPOSans;
        font-weight: bold;
        font-size: 44px;
        background: linear-gradient(180deg, #fff 0%, #7cebff 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }

    /* 图表容器样式 */
    .AmountTop5-echart-bar {
      width: 100%;
      height: 19vh;
      margin-top: 0;
    }
  }
}
</style>
